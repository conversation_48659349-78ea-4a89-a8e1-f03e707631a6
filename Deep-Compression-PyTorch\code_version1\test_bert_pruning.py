#!/usr/bin/env python3
"""
BERT剪枝测试脚本
用于验证主要功能是否正常工作

这个脚本会进行快速测试，确保：
1. 模型能正常加载
2. 剪枝算法能正常执行
3. 基本的训练流程能运行
"""

import torch
import torch.nn as nn
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from bert_pruning import BERTPruningModule, print_model_info
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_model_loading():
    """测试模型加载"""
    logger.info("=== 测试模型加载 ===")
    try:
        tokenizer = AutoTokenizer.from_pretrained("textattack/bert-base-uncased-ag-news")
        model = AutoModelForSequenceClassification.from_pretrained("textattack/bert-base-uncased-ag-news")
        logger.info("✅ 模型加载成功")
        return model, tokenizer
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        return None, None

def test_data_loader_fix(tokenizer):
    """测试修复后的数据加载器"""
    logger.info("\n=== 测试数据加载器修复 ===")
    try:
        from bert_pruning import create_data_loader
        
        # 创建小数据集进行测试
        train_loader, val_loader = create_data_loader(tokenizer, batch_size=4, max_samples=100)
        logger.info(f"✅ 数据加载器创建成功")
        
        # 测试几个批次
        for i, batch in enumerate(val_loader):
            if i >= 3:  # 只测试前3个批次
                break
            logger.info(f"批次 {i+1}: input_ids={batch['input_ids'].shape}, "
                       f"attention_mask={batch['attention_mask'].shape}, "
                       f"labels={batch['label'].shape}")
        
        logger.info("✅ 数据加载器测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 数据加载器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_pruning_module(model):
    """测试剪枝模块"""
    logger.info("\n=== 测试剪枝模块 ===")
    try:
        # 创建剪枝模块
        pruning_module = BERTPruningModule(model)
        logger.info(f"✅ 剪枝模块创建成功，找到 {len(pruning_module.masks)} 个可剪枝层")
        
        # 测试标准差剪枝
        logger.info("测试标准差剪枝...")
        pruning_module.prune_by_std(sensitivity=0.5)  # 使用较高的敏感度进行测试
        
        # 获取统计信息
        stats = pruning_module.get_pruning_stats()
        logger.info(f"✅ 剪枝完成: {stats['pruned_params']:,}/{stats['total_params']:,} "
                   f"({100 * stats['pruned_params'] / stats['total_params']:.1f}%) 参数被剪枝")
        logger.info(f"压缩比: {stats['compression_ratio']:.2f}x")
        
        return pruning_module, stats
    except Exception as e:
        logger.error(f"❌ 剪枝测试失败: {e}")
        return None, None

def test_model_inference(model, tokenizer):
    """测试模型推理"""
    logger.info("\n=== 测试模型推理 ===")
    try:
        # 准备测试数据
        test_texts = [
            "This is a great movie with excellent acting.",
            "The stock market is performing well today.",
            "Scientists discovered a new species in the Amazon.",
            "The latest smartphone has amazing features."
        ]
        
        model.eval()
        with torch.no_grad():
            for i, text in enumerate(test_texts):
                # Tokenize
                inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
                
                # Forward pass
                outputs = model(**inputs)
                logits = outputs.logits
                predicted_class = torch.argmax(logits, dim=-1).item()
                confidence = torch.softmax(logits, dim=-1).max().item()
                
                logger.info(f"文本 {i+1}: 预测类别={predicted_class}, 置信度={confidence:.3f}")
        
        logger.info("✅ 模型推理测试成功")
        return True
    except Exception as e:
        logger.error(f"❌ 模型推理测试失败: {e}")
        return False

def test_sparse_storage(pruning_module):
    """测试稀疏矩阵存储"""
    logger.info("\n=== 测试稀疏矩阵存储 ===")
    try:
        zero_weights_count = 0
        total_weights_count = 0
        total_storage_compression = 0
        layer_count = 0
        
        for name, module in pruning_module.model.named_modules():
            if isinstance(module, nn.Linear) and name in pruning_module.sparse_weights:
                weight = module.weight.data
                sparse_matrix = pruning_module.sparse_weights[name]
                
                # 检查稀疏矩阵重建是否正确
                reconstructed = sparse_matrix.to_dense()
                
                # 验证重建的权重与当前权重一致
                assert torch.allclose(weight, reconstructed, atol=1e-6), f"层 {name} 的稀疏重建不正确"
                
                # 统计零权重
                zero_positions = (weight == 0)
                zero_weights_count += zero_positions.sum().item()
                total_weights_count += weight.numel()
                
                # 计算存储压缩比
                original_size = weight.numel() * 4  # float32
                sparse_size = sparse_matrix.get_storage_size()['total']
                compression = original_size / sparse_size if sparse_size > 0 else 1.0
                total_storage_compression += compression
                layer_count += 1
                
                logger.info(f"层 {name}: 稀疏度={100 * zero_positions.sum().item() / weight.numel():.1f}%, "
                           f"存储压缩={compression:.2f}x")
        
        avg_compression = total_storage_compression / layer_count if layer_count > 0 else 1.0
        logger.info(f"✅ 稀疏存储测试通过: {zero_weights_count}/{total_weights_count} 权重被剪枝")
        logger.info(f"✅ 平均存储压缩比: {avg_compression:.2f}x")
        return True
    except Exception as e:
        logger.error(f"❌ 稀疏存储测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_percentile_pruning(model):
    """测试百分位数剪枝"""
    logger.info("\n=== 测试百分位数剪枝 ===")
    try:
        pruning_module = BERTPruningModule(model)
        pruning_module.prune_by_percentile(percentile=20.0)  # 剪枝最小的20%权重
        
        stats = pruning_module.get_pruning_stats()
        logger.info(f"✅ 百分位数剪枝完成: {stats['pruned_params']:,}/{stats['total_params']:,} "
                   f"({100 * stats['pruned_params'] / stats['total_params']:.1f}%) 参数被剪枝")
        logger.info(f"✅ 存储压缩比: {stats['storage_compression_ratio']:.2f}x")
        
        return True
    except Exception as e:
        logger.error(f"❌ 百分位数剪枝测试失败: {e}")
        return False

def test_sparse_model_save_load(pruning_module, tokenizer):
    """测试稀疏模型保存和加载"""
    logger.info("\n=== 测试稀疏模型保存和加载 ===")
    try:
        import tempfile
        import os
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as f:
            temp_file = f.name
        
        # 保存稀疏模型
        pruning_module.save_sparse_model(temp_file)
        
        # 创建新的模型实例
        from transformers import AutoModelForSequenceClassification
        new_model = AutoModelForSequenceClassification.from_pretrained("textattack/bert-base-uncased-ag-news")
        
        # 创建新的剪枝模块并加载稀疏模型
        new_pruning_module = BERTPruningModule(new_model)
        new_pruning_module.load_sparse_model(temp_file, new_model)
        
        # 验证加载的模型是否正确
        # 测试推理
        test_text = "This is a test sentence for model verification."
        inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True, max_length=512)
        
        # 原模型推理
        pruning_module.model.eval()
        with torch.no_grad():
            original_output = pruning_module.model(**inputs)
        
        # 加载的模型推理
        new_model.eval()
        with torch.no_grad():
            loaded_output = new_model(**inputs)
        
        # 验证输出是否一致
        assert torch.allclose(original_output.logits, loaded_output.logits, atol=1e-5), "模型保存加载后输出不一致"
        
        # 清理临时文件
        os.unlink(temp_file)
        
        logger.info("✅ 稀疏模型保存和加载测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 稀疏模型保存加载测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    logger.info("开始BERT剪枝功能测试...")
    logger.info("=" * 60)
    
    # 测试结果统计
    tests_passed = 0
    total_tests = 8
    
    # 1. 测试模型加载
    model, tokenizer = test_model_loading()
    if model is not None:
        tests_passed += 1
        
        # 打印模型信息
        print_model_info(model)
    else:
        logger.error("模型加载失败，跳过后续测试")
        return
    
    # 2. 测试数据加载器修复
    if test_data_loader_fix(tokenizer):
        tests_passed += 1
    
    # 3. 测试剪枝模块
    pruning_module, stats = test_pruning_module(model)
    if pruning_module is not None:
        tests_passed += 1
    
    # 4. 测试推理（剪枝后）
    if test_model_inference(model, tokenizer):
        tests_passed += 1
    
    # 5. 测试稀疏存储
    if pruning_module is not None and test_sparse_storage(pruning_module):
        tests_passed += 1
    
    # 6. 重新加载模型进行百分位数剪枝测试
    model2, _ = test_model_loading()
    if model2 is not None and test_percentile_pruning(model2):
        tests_passed += 1
    
    # 7. 测试推理（百分位数剪枝后）
    if test_model_inference(model2, tokenizer):
        tests_passed += 1
    
    # 8. 测试稀疏模型保存和加载
    if pruning_module is not None and test_sparse_model_save_load(pruning_module, tokenizer):
        tests_passed += 1
    
    # 总结测试结果
    logger.info("\n" + "=" * 60)
    logger.info("测试结果总结:")
    logger.info(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        logger.info("🎉 所有测试通过！BERT剪枝功能工作正常")
        return True
    else:
        logger.warning(f"⚠️  有 {total_tests - tests_passed} 个测试失败")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1) 